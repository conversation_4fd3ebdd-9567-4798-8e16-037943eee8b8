#!/usr/bin/env python3

import os
import subprocess
import time
import tempfile

class InteractiveCmdSession:
    """Manages a persistent interactive cmd window for AI command execution."""
    
    def __init__(self):
        self.process = None
        self.temp_batch_file = None
        self.cmd_input_file = None
        self.cmd_output_file = None
        self.is_running = False
        
    def start(self):
        """Start the interactive cmd session."""
        try:
            if os.name == 'nt':  # Windows
                # Create temporary files for communication
                self.cmd_input_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False)
                self.cmd_output_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False)
                self.cmd_input_file.close()
                self.cmd_output_file.close()
                
                # Create a batch file that monitors for commands and executes them
                self.temp_batch_file = tempfile.NamedTemporaryFile(mode='w', suffix='.bat', delete=False)
                batch_content = f'''@echo off
setlocal enabledelayedexpansion
title AI Interactive Command Window
color 0A
echo ========================================
echo AI Interactive Command Session Started
echo ========================================
echo Current Directory: {os.getcwd()}
echo.
echo This window will execute AI commands interactively.
echo Waiting for AI commands...
echo.
cd /d "{os.getcwd()}"

:loop
if exist "{self.cmd_input_file.name}" (
    for /f "delims=" %%i in ({self.cmd_input_file.name}) do (
        if not "%%i"=="" (
            echo.
            echo ========================================
            echo AI Command: %%i
            echo ========================================
            %%i > "{self.cmd_output_file.name}" 2>&1
            echo.
            echo ========================================
            echo Command completed. Return code: !ERRORLEVEL!
            echo ========================================
            echo.
        )
    )
    del "{self.cmd_input_file.name}" 2>nul
)
timeout /t 1 /nobreak >nul
goto loop
'''
                self.temp_batch_file.write(batch_content)
                self.temp_batch_file.close()
                
                # Start the interactive cmd window
                self.process = subprocess.Popen(
                    f'start "AI Interactive CMD" cmd /c "{self.temp_batch_file.name}"',
                    shell=True,
                    cwd=os.getcwd()
                )
                
                self.is_running = True
                print("✓ Interactive CMD window opened - Check the new window!")
                print("Commands will be executed in the visible CMD window")
                
                # Wait a moment for the window to initialize
                time.sleep(2)
                
            else:
                print("This demo is designed for Windows systems")
                self.is_running = False
            
        except Exception as e:
            print(f"❌ Failed to start interactive CMD session: {e}")
            self.is_running = False
    
    def execute_command(self, command: str, working_directory: str = None, timeout: float = 30.0) -> str:
        """Execute a command in the interactive CMD window."""
        if not self.is_running:
            return "Error: Interactive CMD session not running"
        
        try:
            # Prepare the command with working directory change if needed
            full_command = command
            if working_directory:
                cwd = os.path.abspath(working_directory)
                full_command = f'cd /d "{cwd}" && {command}'
            
            # Clear any existing output file
            if os.path.exists(self.cmd_output_file.name):
                try:
                    os.remove(self.cmd_output_file.name)
                except:
                    pass
            
            # Write command to input file for the CMD window to execute
            with open(self.cmd_input_file.name, 'w') as f:
                f.write(full_command)
            
            print(f"📤 Sent command to CMD window: {command}")
            
            # Wait for the command to be executed and output to be written
            start_time = time.time()
            output_content = ""
            
            while time.time() - start_time < timeout:
                # Check if the input file has been consumed (deleted by the batch script)
                if not os.path.exists(self.cmd_input_file.name):
                    # Wait a bit more for output to be written
                    time.sleep(1)
                    
                    # Try to read the output
                    if os.path.exists(self.cmd_output_file.name):
                        try:
                            with open(self.cmd_output_file.name, 'r', encoding='utf-8', errors='ignore') as f:
                                output_content = f.read()
                            break
                        except:
                            pass
                
                time.sleep(0.5)
            
            # If we still don't have output, try one more time
            if not output_content and os.path.exists(self.cmd_output_file.name):
                try:
                    with open(self.cmd_output_file.name, 'r', encoding='utf-8', errors='ignore') as f:
                        output_content = f.read()
                except:
                    pass
            
            # Clean up output file for next command
            if os.path.exists(self.cmd_output_file.name):
                try:
                    os.remove(self.cmd_output_file.name)
                except:
                    pass
            
            if not output_content:
                output_content = "(No output captured - check the CMD window)"
            
            return f"Command: {command}\nExecuted in interactive CMD window\n\nOutput:\n{output_content}"
            
        except Exception as e:
            return f"Command: {command}\nError: {str(e)}"
    
    def stop(self):
        """Stop the interactive cmd session."""
        self.is_running = False
        
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                try:
                    self.process.kill()
                except:
                    pass
        
        # Clean up all temporary files
        temp_files = [
            self.temp_batch_file,
            self.cmd_input_file,
            self.cmd_output_file
        ]
        
        for temp_file in temp_files:
            if temp_file and hasattr(temp_file, 'name') and os.path.exists(temp_file.name):
                try:
                    os.unlink(temp_file.name)
                except:
                    pass
        
        print("⚠ Interactive CMD session stopped")

def test_real_interactive():
    """Test the real interactive CMD session."""
    print("🤖 Testing Real Interactive CMD Session")
    print("=" * 50)
    
    # Start interactive session
    cmd_session = InteractiveCmdSession()
    cmd_session.start()
    
    if not cmd_session.is_running:
        print("Failed to start CMD session")
        return
    
    print("✅ CMD window should now be visible and waiting for commands")
    print("Watch the CMD window as commands are executed!")
    
    # Test commands
    test_commands = [
        "echo Hello from AI in the interactive window!",
        "dir",
        "echo Current time: %TIME%",
        "python --version",
        "echo This command is running in the visible CMD window",
    ]
    
    for i, command in enumerate(test_commands, 1):
        print(f"\n🤖 Test {i}: {command}")
        output = cmd_session.execute_command(command, timeout=10)
        print(f"📥 AI received output:\n{output}")
        print("👀 Check the CMD window to see the command execution!")
        time.sleep(3)
    
    print("\n🎉 All tests completed!")
    print("💡 You should have seen all commands execute in the CMD window")
    
    # Keep session alive for a moment
    print("\nKeeping session alive for 10 seconds...")
    time.sleep(10)
    
    # Stop the session
    print("\n🤖 Stopping interactive session...")
    cmd_session.stop()
    
    print("Test completed!")

if __name__ == "__main__":
    test_real_interactive()
