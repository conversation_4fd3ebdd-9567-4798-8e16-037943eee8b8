#!/usr/bin/env python3

import os
import subprocess
import threading
import queue
import time
import atexit

class InteractiveCmdSession:
    """Manages a persistent interactive cmd window for AI command execution."""
    
    def __init__(self):
        self.process = None
        self.input_queue = queue.Queue()
        self.output_queue = queue.Queue()
        self.reader_thread = None
        self.writer_thread = None
        self.is_running = False
        
    def start(self):
        """Start the interactive cmd session."""
        try:
            # Start cmd process with visible window
            if os.name == 'nt':  # Windows
                # Create a new visible command prompt window
                self.process = subprocess.Popen(
                    'cmd.exe',
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    bufsize=0,  # Unbuffered
                    creationflags=subprocess.CREATE_NEW_CONSOLE
                )
            else:
                # For non-Windows systems, use terminal
                self.process = subprocess.Popen(
                    ['bash'],
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    bufsize=0
                )
            
            self.is_running = True
            
            # Start reader thread to capture output
            self.reader_thread = threading.Thread(target=self._read_output, daemon=True)
            self.reader_thread.start()
            
            # Start writer thread to send commands
            self.writer_thread = threading.Thread(target=self._write_input, daemon=True)
            self.writer_thread.start()
            
            # Send initial setup commands
            self.send_command("echo AI Command Session Started")
            self.send_command(f"cd /d \"{os.getcwd()}\"")  # Set working directory
            
            print("✓ Interactive CMD session started")
            
        except Exception as e:
            print(f"❌ Failed to start interactive CMD session: {e}")
            self.is_running = False
    
    def _read_output(self):
        """Thread function to read output from cmd process."""
        try:
            while self.is_running and self.process and self.process.poll() is None:
                line = self.process.stdout.readline()
                if line:
                    self.output_queue.put(line.rstrip('\n\r'))
        except Exception as e:
            print(f"❌ Error reading from CMD: {e}")
    
    def _write_input(self):
        """Thread function to write input to cmd process."""
        try:
            while self.is_running:
                try:
                    command = self.input_queue.get(timeout=1)
                    if command and self.process and self.process.stdin:
                        self.process.stdin.write(command + '\n')
                        self.process.stdin.flush()
                except queue.Empty:
                    continue
                except Exception as e:
                    print(f"❌ Error writing to CMD: {e}")
                    break
        except Exception as e:
            print(f"❌ Error in write thread: {e}")
    
    def send_command(self, command: str):
        """Send a command to the interactive cmd session."""
        if self.is_running:
            self.input_queue.put(command)
    
    def get_output(self, timeout: float = 5.0):
        """Get output from the cmd session with timeout."""
        output_lines = []
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                line = self.output_queue.get(timeout=0.1)
                output_lines.append(line)
            except queue.Empty:
                # If we have some output and haven't seen new output for a bit, return what we have
                if output_lines and time.time() - start_time > 1.0:
                    break
                continue
        
        return output_lines
    
    def execute_command(self, command: str, timeout: float = 30.0) -> str:
        """Execute a command and return the output."""
        if not self.is_running:
            return "Error: Interactive CMD session not running"
        
        # Clear any existing output
        while not self.output_queue.empty():
            try:
                self.output_queue.get_nowait()
            except queue.Empty:
                break
        
        # Send the command
        self.send_command(command)
        
        # Wait for output
        output_lines = self.get_output(timeout)
        
        return '\n'.join(output_lines) if output_lines else "(No output)"
    
    def stop(self):
        """Stop the interactive cmd session."""
        self.is_running = False
        
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                try:
                    self.process.kill()
                except:
                    pass
        
        print("⚠ Interactive CMD session stopped")

def test_interactive_cmd():
    """Test the interactive cmd session."""
    print("Testing Interactive CMD Session...")
    
    # Create and start session
    cmd_session = InteractiveCmdSession()
    cmd_session.start()
    
    if not cmd_session.is_running:
        print("Failed to start CMD session")
        return
    
    # Wait a moment for initialization
    time.sleep(2)
    
    # Test some commands
    test_commands = [
        "echo Hello from AI!",
        "dir",
        "echo Current directory: %CD%",
        "python --version"
    ]
    
    for command in test_commands:
        print(f"\n⚡ Executing: {command}")
        output = cmd_session.execute_command(command, timeout=5)
        print(f"Output:\n{output}")
        time.sleep(1)
    
    # Stop the session
    print("\nStopping CMD session...")
    cmd_session.stop()
    
    print("Test completed!")

if __name__ == "__main__":
    test_interactive_cmd()
