#!/usr/bin/env python3

import os
import subprocess
import time
import tempfile

class InteractiveCmdSession:
    """Manages a persistent interactive cmd window for AI command execution."""
    
    def __init__(self):
        self.process = None
        self.temp_batch_file = None
        self.is_running = False
        
    def start(self):
        """Start the interactive cmd session."""
        try:
            if os.name == 'nt':  # Windows
                # Create a temporary batch file to keep the window open
                self.temp_batch_file = tempfile.NamedTemporaryFile(mode='w', suffix='.bat', delete=False)
                batch_content = f'''@echo off
title AI Interactive Command Window
echo ========================================
echo AI Interactive Command Session Started
echo ========================================
echo Current Directory: {os.getcwd()}
echo.
echo This window will show AI commands and their results.
echo The AI will execute commands here interactively.
echo.
cd /d "{os.getcwd()}"
cmd /k
'''
                self.temp_batch_file.write(batch_content)
                self.temp_batch_file.close()
                
                # Start the interactive cmd window
                self.process = subprocess.Popen(
                    f'start "AI Interactive CMD" "{self.temp_batch_file.name}"',
                    shell=True,
                    cwd=os.getcwd()
                )
                
                self.is_running = True
                print("✓ Interactive CMD window opened - Check the new window!")
                print("Commands will be executed in the visible CMD window")
                
            else:
                print("This demo is designed for Windows systems")
                self.is_running = False
            
        except Exception as e:
            print(f"❌ Failed to start interactive CMD session: {e}")
            self.is_running = False
    
    def execute_command(self, command: str, working_directory: str = None, timeout: float = 30.0) -> str:
        """Execute a command in the interactive CMD window."""
        if not self.is_running:
            return "Error: Interactive CMD session not running"
        
        try:
            # Set working directory if provided
            if working_directory:
                cwd = os.path.abspath(working_directory)
            else:
                cwd = os.getcwd()
            
            # Execute the command and capture output for AI
            result = subprocess.run(
                command,
                shell=True,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            # Also send the command to the visible window for user to see
            # Create a batch file that will execute in the visible window
            temp_cmd_file = tempfile.NamedTemporaryFile(mode='w', suffix='.bat', delete=False)
            batch_content = f'''@echo off
echo.
echo ========================================
echo AI Executing: {command}
echo ========================================
cd /d "{cwd}"
{command}
echo.
echo ========================================
echo Command completed. Return code: %ERRORLEVEL%
echo ========================================
echo.
'''
            temp_cmd_file.write(batch_content)
            temp_cmd_file.close()
            
            # Send to visible window (this will show the command execution)
            subprocess.Popen(
                f'"{temp_cmd_file.name}"',
                shell=True,
                cwd=cwd
            )
            
            # Clean up temp file after a delay
            def cleanup():
                time.sleep(2)
                try:
                    os.unlink(temp_cmd_file.name)
                except:
                    pass
            
            import threading
            threading.Thread(target=cleanup, daemon=True).start()
            
            # Format output for AI
            output_parts = []
            if result.stdout:
                output_parts.append(f"STDOUT:\n{result.stdout}")
            if result.stderr:
                output_parts.append(f"STDERR:\n{result.stderr}")
            
            output = "\n\n".join(output_parts) if output_parts else "(No output)"
            
            return f"Command: {command}\nReturn code: {result.returncode}\nWorking directory: {cwd}\n\n{output}"
            
        except subprocess.TimeoutExpired:
            return f"Command: {command}\nError: Command timed out after {timeout} seconds"
        except Exception as e:
            return f"Command: {command}\nError: {str(e)}"
    
    def stop(self):
        """Stop the interactive cmd session."""
        self.is_running = False
        
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except:
                try:
                    self.process.kill()
                except:
                    pass
        
        # Clean up temp batch file
        if self.temp_batch_file and os.path.exists(self.temp_batch_file.name):
            try:
                os.unlink(self.temp_batch_file.name)
            except:
                pass
        
        print("⚠ Interactive CMD session stopped")

def simulate_ai_commands():
    """Simulate AI executing commands in the interactive session."""
    print("🤖 AI Demo: Interactive Command Execution")
    print("=" * 50)
    
    # Start interactive session
    cmd_session = InteractiveCmdSession()
    cmd_session.start()
    
    if not cmd_session.is_running:
        print("Failed to start CMD session")
        return
    
    # Wait for initialization
    print("Waiting for CMD window to initialize...")
    time.sleep(3)
    
    # Simulate AI executing various commands
    ai_commands = [
        ("Checking current directory", "echo Current directory: %CD%"),
        ("Listing files", "dir"),
        ("Checking Python version", "python --version"),
        ("Creating a test file", "echo Hello from AI Interactive Session! > ai_interactive_test.txt"),
        ("Reading the test file", "type ai_interactive_test.txt"),
        ("Checking if file exists", "if exist ai_interactive_test.txt echo File exists!"),
        ("Running a Python command", "python -c \"print('Hello from Python in interactive CMD!')\""),
        ("Cleaning up", "del ai_interactive_test.txt"),
    ]
    
    for description, command in ai_commands:
        print(f"\n🤖 AI: {description}")
        print(f"⚡ Executing: {command}")
        
        output = cmd_session.execute_command(command, timeout=10)
        print(f"📄 AI received output:\n{output}")
        
        print("💡 Check the CMD window to see the command execution!")
        time.sleep(4)  # Pause between commands to see the effect
    
    print("\n🤖 AI: All tasks completed!")
    print("💡 Notice: All commands were executed in the same persistent CMD window")
    print("💡 You can see the commands being executed in real-time in the CMD window")
    print("💡 The AI receives the output while the user sees the execution")
    
    # Keep session alive for a moment
    print("\nKeeping session alive for 10 seconds...")
    time.sleep(10)
    
    # Stop the session
    print("\n🤖 AI: Stopping interactive session...")
    cmd_session.stop()
    
    print("Demo completed!")

if __name__ == "__main__":
    simulate_ai_commands()
